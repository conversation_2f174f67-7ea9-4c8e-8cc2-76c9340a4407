"use client";

import React from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type IAsynDropdownProps = {
  defaultValue: string;
  param: string;
  options: {
    label: string;
    value: string;
  }[];
  Icon?: React.ReactElement
};

const AsyncDropdown = ({
  defaultValue,
  param,
  options,
  Icon,
}: IAsynDropdownProps) => {
  const params = useSearchParams();
  const { replace } = useRouter();
  const pathName = usePathname();

  const handleChange = (value: string) => {
    const searchParams = new URLSearchParams(params);
    if (value) {
      searchParams.set(param, value);
    } else {
      searchParams.delete(param);
    }
    searchParams.set("page", "1");
    searchParams.toString();

    replace(`${pathName}?${searchParams.toString()}`);
  };
  return (
    <Select defaultValue={defaultValue} onValueChange={handleChange}>
      <SelectTrigger className="w-[175px]">
        {Icon && <Icon />}
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default AsyncDropdown;
