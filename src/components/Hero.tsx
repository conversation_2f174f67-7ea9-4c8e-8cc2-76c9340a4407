import { But<PERSON> } from "@/components/ui/button";
import { Upload, Play, Flame } from "lucide-react";

export function Hero() {
  return (
    <section className="relative py-10 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-subtle" />

      <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-4xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center space-x-2 rounded-full border px-3 py-1 text-sm bg-accent/50 text-accent-foreground mb-8">
            <Flame className="h-4 w-4" />
            <span>Live CV Roasting Platform</span>
          </div>

          {/* Main headline */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
            Submit Your CV to
            <span className="block bg-ring bg-clip-text text-transparent">
              Content Creators
            </span>
          </h1>

          {/* Subheadline */}
          <p className="text-lg sm:text-xl text-muted-foreground mb-10 max-w-2xl mx-auto leading-relaxed">
            Find collection forms from your favorite content creators. Submit
            your CV and get roasted on their streams with personalized feedback.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Button variant="fire" size="lg" className="w-full sm:w-auto">
              <Upload className="mr-2 h-5 w-5" />
              Browse Collection Forms
            </Button>
            <Button variant="outline" size="lg" className="w-full sm:w-auto">
              <Flame className="mr-2 h-5 w-5" />
              Create Collection Form
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary mb-1">1,200+</div>
              <div className="text-sm text-muted-foreground">CVs Submitted</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary mb-1">50+</div>
              <div className="text-sm text-muted-foreground">
                Content Creators
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary mb-1">95%</div>
              <div className="text-sm text-muted-foreground">
                Positive Feedback
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
