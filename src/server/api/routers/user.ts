import { Zod<PERSON><PERSON><PERSON>, z } from "zod";

import {
	createTRPCRouter,
	protectedProcedure,
	publicProcedure,
} from "@/server/api/trpc";
import { db } from "@/server/db";
import { comparePassword, hashPassword } from "@/utils/bcrypt";
import { signinSchema, signupSchema } from "@/validators/auth";
import { TRPCError } from "@trpc/server";

export const userRouter = createTRPCRouter({
	register: publicProcedure.input(signupSchema).mutation(async ({ input }) => {
		try {
			await signupSchema.parseAsync(input);
			const { name, email, password, confirmPassword } = input;
			if (password !== confirmPassword) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Passwords do not match",
				});
			}

			const existingUser = await db.user.findUnique({ where: { email } });
			if (existingUser) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "User already exists",
				});
			}

			const hashedPassword = await hashPassword(password);

			await db.user.create({
				data: {
					name,
					email,
					password: hashedPassword,
				},
			});
		} catch (error) {
			if (error instanceof TRPCError) {
				throw error;
			}
			if (error instanceof ZodError) {
				throw new TRPCError({ code: "BAD_REQUEST", message: "Invalid input" });
			}
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Something went wrong",
			});
		}
	}),
	signin: publicProcedure.input(signinSchema).mutation(async ({ input }) => {
		try {
			await signinSchema.parseAsync(input);
			const { email, password } = input;

			const existingUser = await db.user.findUnique({ where: { email } });
			if (!existingUser) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid credentials",
				});
			}

			//if user does not have password than means they signed up with google
			if (!existingUser.password) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Please signin with google",
				});
			}

			const isPasswordValid = await comparePassword(
				password,
				existingUser.password,
			);
			if (!isPasswordValid) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid credentials",
				});
			}

			return existingUser;
		} catch (error) {
			if (error instanceof TRPCError) {
				throw error;
			}
			if (error instanceof ZodError) {
				throw new TRPCError({ code: "BAD_REQUEST", message: "Invalid input" });
			}
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Something went wrong",
			});
		}
	}),
});
