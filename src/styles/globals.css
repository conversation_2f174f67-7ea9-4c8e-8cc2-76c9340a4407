@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
	--font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
		"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
	/* Colors (auto-mapped to bg-*, text-*, border-*) */
	--color-background: hsl(0 0% 99%);
	--color-foreground: hsl(222 84% 4.9%);

	--color-card: hsl(0 0% 100%);
	--color-card-foreground: hsl(222 84% 4.9%);

	--color-popover: hsl(0 0% 100%);
	--color-popover-foreground: hsl(222 84% 4.9%);

	--color-primary: hsl(14 100% 57%);
	--color-primary-foreground: hsl(0 0% 100%);

	--color-secondary: hsl(0 0% 96%);
	--color-secondary-foreground: hsl(222 84% 4.9%);

	--color-muted: hsl(0 0% 96%);
	--color-muted-foreground: hsl(215 16% 47%);

	--color-accent: hsl(24 100% 95%);
	--color-accent-foreground: hsl(14 100% 40%);

	--color-destructive: hsl(0 84% 60%);
	--color-destructive-foreground: hsl(0 0% 100%);

	/* Borders / Inputs */
	--color-border: hsl(220 13% 91%);
	--color-input: hsl(220 13% 91%);
	--color-ring: hsl(14 100% 57%);

	/* Gradients & Shadows (manual use) */
	--gradient-fire: linear-gradient(135deg, hsl(14 100% 57%), hsl(24 100% 50%));
	--gradient-subtle: linear-gradient(180deg, hsl(0 0% 100%), hsl(0 0% 98%));

	--shadow-fire: 0 10px 30px -10px hsl(14 100% 57% / 0.3);
	--shadow-soft: 0 4px 20px -4px hsl(222 84% 4.9% / 0.1);

	/* Radius */
	--radius-default: 0.75rem;

	/* Sidebar colors */
	--color-sidebar-background: hsl(0 0% 98%);
	--color-sidebar-foreground: hsl(240 5.3% 26.1%);
	--color-sidebar-primary: hsl(240 5.9% 10%);
	--color-sidebar-primary-foreground: hsl(0 0% 98%);
	--color-sidebar-accent: hsl(240 4.8% 95.9%);
	--color-sidebar-accent-foreground: hsl(240 5.9% 10%);
	--color-sidebar-border: hsl(220 13% 91%);
	--color-sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@layer base {
	* {
		border-color: hsl(var(--color-border));
	}

	body {
		@apply bg-background text-foreground font-sans;
	}
}

/* @theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
} */

:root {
	--radius: 0.625rem;
	--background: oklch(1 0 0);
	--foreground: oklch(0.145 0 0);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.145 0 0);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.145 0 0);
	--primary: oklch(0.205 0 0);
	--primary-foreground: oklch(0.985 0 0);
	--secondary: oklch(0.97 0 0);
	--secondary-foreground: oklch(0.205 0 0);
	--muted: oklch(0.97 0 0);
	--muted-foreground: oklch(0.556 0 0);
	--accent: oklch(0.97 0 0);
	--accent-foreground: oklch(0.205 0 0);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.922 0 0);
	--input: oklch(0.922 0 0);
	--ring: oklch(0.708 0 0);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0 0);
	--sidebar-foreground: oklch(0.145 0 0);
	--sidebar-primary: oklch(0.205 0 0);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.97 0 0);
	--sidebar-accent-foreground: oklch(0.205 0 0);
	--sidebar-border: oklch(0.922 0 0);
	--sidebar-ring: oklch(0.708 0 0);
}

.dark {
	--background: oklch(0.145 0 0);
	--foreground: oklch(0.985 0 0);
	--card: oklch(0.205 0 0);
	--card-foreground: oklch(0.985 0 0);
	--popover: oklch(0.205 0 0);
	--popover-foreground: oklch(0.985 0 0);
	--primary: oklch(0.922 0 0);
	--primary-foreground: oklch(0.205 0 0);
	--secondary: oklch(0.269 0 0);
	--secondary-foreground: oklch(0.985 0 0);
	--muted: oklch(0.269 0 0);
	--muted-foreground: oklch(0.708 0 0);
	--accent: oklch(0.269 0 0);
	--accent-foreground: oklch(0.985 0 0);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(1 0 0 / 10%);
	--input: oklch(1 0 0 / 15%);
	--ring: oklch(0.556 0 0);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.205 0 0);
	--sidebar-foreground: oklch(0.985 0 0);
	--sidebar-primary: oklch(0.488 0.243 264.376);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.269 0 0);
	--sidebar-accent-foreground: oklch(0.985 0 0);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: oklch(0.556 0 0);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
}
