import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, FileText, Filter, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { Footer } from "@/components/Footer";
import SearchInput from "@/components/AsyncSearchInput";
import AsyncDropdown from "@/components/AsyncDropdown";
import { FORM_CATEGORIES } from "@/common/constants";

export default async function AllForms(props: {
  searchParams?: Promise<{
    q?: string;
    page?: string;
    category?: string;
    status?: string;
  }>;
}) {
  const searchParams = await props.searchParams;
  const searchQuery = searchParams?.q || "";
  const filterCategory = searchParams?.category || "all";
  const filterStatus = searchParams?.status || "all";

  const FORM_CATEFORIES_WITH_ALL = [
    { label: "All Categories", value: "all" },
    ...FORM_CATEGORIES,
  ];

  const FORM_STATUS_OPTIONS = [
    { label: "All Status", value: "all" },
    { label: "Open", value: "active" },
    { label: "Closed", value: "closed" },
  ];

  const filteredForms = [
    {
      id: 1,
      creator: "TechReviewer_Pro",
      title: "Tech Resume Review Collection",
      submissions: 47,
      maxSubmissions: 50,
      deadline: "Dec 20, 2024",
      isActive: true,
      category: "Tech",
      description:
        "Collecting tech resumes for my next YouTube roasting series",
      platform: "YouTube",
      streamUrl: "youtube.com/techreviewer",
    },
    {
      id: 2,
      creator: "HR_Brutalist",
      title: "Corporate CV Destruction",
      submissions: 23,
      maxSubmissions: 30,
      deadline: "Dec 18, 2024",
      isActive: true,
      category: "Business",
      description:
        "Submit your corporate CVs for brutal honest feedback on Twitch",
      platform: "Twitch",
      streamUrl: "twitch.tv/hr_brutalist",
    },
    {
      id: 3,
      creator: "DesignCritic",
      title: "Creative Portfolio Reviews",
      submissions: 31,
      maxSubmissions: 25,
      deadline: "Dec 15, 2024",
      isActive: false,
      category: "Design",
      description: "Design portfolios and creative CVs for upcoming stream",
      platform: "YouTube",
      streamUrl: "youtube.com/designcritic",
    },
    {
      id: 4,
      creator: "StartupGuru",
      title: "Entrepreneur CV Roast",
      submissions: 15,
      maxSubmissions: 40,
      deadline: "Dec 25, 2024",
      isActive: true,
      category: "Startup",
      description: "Startup founders and entrepreneurs, let's roast your CVs",
      platform: "LinkedIn Live",
      streamUrl: "linkedin.com/in/startupguru",
    },
    {
      id: 5,
      creator: "CodeMaster",
      title: "Developer Portfolio Destruction",
      submissions: 67,
      maxSubmissions: 60,
      deadline: "Dec 22, 2024",
      isActive: false,
      category: "Tech",
      description:
        "Full-stack developers, bring your portfolios for intense feedback",
      platform: "Twitch",
      streamUrl: "twitch.tv/codemaster",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <main>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Header */}
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-muted-foreground hover:text-foreground mb-4 transition-colors"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>

            <div className="text-center mb-8">
              <h1 className="text-3xl md:text-4xl font-bold mb-4">
                All CV Collection Forms
              </h1>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Browse all available CV collection forms from content creators.
                Submit your CV and get roasted!
              </p>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <SearchInput defaultValue={searchQuery} />
              <AsyncDropdown
                defaultValue={filterCategory}
                param="category"
                options={FORM_CATEFORIES_WITH_ALL}
                Icon={Filter}
              />
              <AsyncDropdown
                defaultValue={filterStatus}
                param="status"
                options={FORM_STATUS_OPTIONS}
              />
            </div>
          </div>

          {/* Forms Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredForms.map((form) => (
              <Card
                key={form.id}
                className="relative overflow-hidden hover:shadow-lg transition-all duration-300"
              >
                {form.isActive && (
                  <div className="absolute top-3 right-3 z-10">
                    <Badge
                      variant="default"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      OPEN
                    </Badge>
                  </div>
                )}
                {!form.isActive && (
                  <div className="absolute top-3 right-3 z-10">
                    <Badge variant="secondary">CLOSED</Badge>
                  </div>
                )}

                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-1 line-clamp-2">
                        {form.title}
                      </CardTitle>
                      <CardDescription className="font-medium">
                        by {form.creator}
                      </CardDescription>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                    {form.description}
                  </p>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <FileText className="h-4 w-4" />
                      <span>
                        {form.submissions}/{form.maxSubmissions} submitted
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{form.deadline}</span>
                    </div>
                  </div>

                  <div className="w-full bg-secondary rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all"
                      style={{
                        width: `${
                          (form.submissions / form.maxSubmissions) * 100
                        }%`,
                      }}
                    />
                  </div>

                  <div className="text-xs text-muted-foreground">
                    Streaming on:{" "}
                    <span className="font-medium text-foreground">
                      {form.platform}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge variant="secondary">{form.category}</Badge>
                    <Button
                      variant={form.isActive ? "fire" : "outline"}
                      size="sm"
                      className="w-32"
                      disabled={!form.isActive}
                      asChild={form.isActive}
                    >
                      {form.isActive ? (
                        <a href="/submit-cv">Get Grilled 🔥</a>
                      ) : (
                        "Closed"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredForms.length === 0 && (
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No forms found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search or filters to find more forms
              </p>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
