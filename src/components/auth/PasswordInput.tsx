"use client";

import { Eye, EyeOff } from "lucide-react";
import React from "react";

import { Input } from "@/components/ui/input";

const PasswordInput = (props: React.InputHTMLAttributes<HTMLInputElement>) => {
	const [show, setShow] = React.useState(false);
	return (
		<div className="relative">
			<Input {...props} type={show ? "text" : "password"} />
			<button
				type="button"
				onClick={() => setShow(!show)}
				className="absolute top-3 right-3 text-muted-foreground hover:text-foreground"
			>
				{!show ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
			</button>
		</div>
	);
};

export default PasswordInput;
