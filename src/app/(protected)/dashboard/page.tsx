import { auth } from "@/server/auth";
import Link from "next/link";
import React from "react";

import type { AnyFieldApi } from "@tanstack/react-form";

function FieldInfo({ field }: { field: AnyFieldApi }) {
	return (
		<>
			{field.state.meta.isTouched && !field.state.meta.isValid ? (
				<em>{field.state.meta.errors.join(", ")}</em>
			) : null}
			{field.state.meta.isValidating ? "Validating..." : null}
		</>
	);
}

const DashboardPage = async () => {
	const session = await auth();

	return (
		<div>
			<h1>Dashboard for {session?.user.name}</h1>
			<Link
				href={session ? "/api/auth/signout" : "/api/auth/signin"}
				className="rounded-full bg-white/10 px-10 py-3 font-semibold no-underline transition hover:bg-white/20"
			>
				{session ? "Sign out" : "Sign in"}
			</Link>

			<div>
				<h1>Simple Form Example</h1>
			</div>
		</div>
	);
};

export default DashboardPage;
