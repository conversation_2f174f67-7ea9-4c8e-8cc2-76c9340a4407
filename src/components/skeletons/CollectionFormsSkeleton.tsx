import { Users } from "lucide-react";

import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

const CollectionFormsSkeleton = () => {
  return (
    <section id="forms" className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Active Collection Forms
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Submit your CV to content creators and get featured in their
            roasting streams
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {[1, 2, 3].map((form, index) => {
            return (
              <Card
                key={index}
                className="relative overflow-hidden hover:shadow-lg transition-all duration-300"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[250px]" />
                    </div>
                  </div>
                    <Skeleton className="h-4 w-[250px]" />
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Skeleton className="h-4 w-[100px]" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <Skeleton className="h-4 w-[100px]" />
                  </div>

                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-[50px]" />
                    <Skeleton className="h-8 w-32 rounded-2xl" />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="text-center">
          <Button variant="outline" size="lg" asChild>
            <a href="/all-forms">
              <Users className="mr-2 h-5 w-5" />
              View All Forms
            </a>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CollectionFormsSkeleton;
