import { z } from "zod";

export const signinSchema = z.object({
	email: z.string().email().min(1, "Email is required"),
	password: z
		.string()
		.min(1, "Password is required")
		.min(8, "Password must be more than 8 characters")
		.max(32, "Password must be less than 32 characters"),
});

export const signupSchema = z.object({
	name: z.string().min(1, "Name is required"),
	email: z.string().email(),
	password: z
		.string()
		.min(1, "Password is required")
		.min(8, "Password must be more than 8 characters")
		.max(32, "Password must be less than 32 characters"),
	confirmPassword: z
		.string()
		.min(1, "Password is required")
		.min(8, "Password must be more than 8 characters")
		.max(32, "Password must be less than 32 characters"),
});
