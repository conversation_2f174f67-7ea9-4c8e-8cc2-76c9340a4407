"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Flame, Menu } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="sticky top-0 z-50 border-b bg-background/80 backdrop-blur-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <Link href="/" className="flex items-center space-x-2">
              <Flame className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">grillMyCV</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <a
              href="#forms"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Browse Forms
            </a>
            <a
              href="#how-it-works"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              How it works
            </a>
            <a
              href="/dashboard"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Creator Dashboard
            </a>
          </div>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="fire" size="sm" className="flex-1" asChild>
              <Link href="/form/create">Create Form</Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 space-y-4 border-t">
            <div className="flex flex-col space-y-3">
              <a
                href="#forms"
                className="text-sm font-medium text-muted-foreground"
              >
                Browse Forms
              </a>
              <a
                href="#how-it-works"
                className="text-sm font-medium text-muted-foreground"
              >
                How it works
              </a>
              <a
                href="/dashboard"
                className="text-sm font-medium text-muted-foreground"
              >
                Creator Dashboard
              </a>
              <div className="flex space-x-3 pt-3">
                <Button variant="fire" size="sm" className="flex-1" asChild>
                  <Link href="/form/create">Create Form</Link>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
