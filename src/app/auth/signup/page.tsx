"use client";

import { useForm } from "@tanstack/react-form";
import { Mail, User } from "lucide-react";
import { signIn } from "next-auth/react";
import Link from "next/link";
import React from "react";
import { toast } from "sonner";
import type { z } from "zod";

import FieldError from "@/components/FieldError";
import GoogleButton from "@/components/auth/GoogleButton";
import PasswordInput from "@/components/auth/PasswordInput";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { api } from "@/trpc/react";
import { signupSchema } from "@/validators/auth";

type SignupFormType = z.infer<typeof signupSchema>;

const formDefaultValues: SignupFormType = {
  name: "",
  email: "",
  password: "",
  confirmPassword: "",
};

const SignupPage = () => {
  const userMutation = api.user.register.useMutation({
    onSuccess: (data, value) => {
      toast.success("Account created successfully");
      signIn("credentials", {
        email: value.email,
        password: value.password,
        redirectTo: "/dashboard",
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const form = useForm({
    defaultValues: formDefaultValues,
    validators: {
      onChange: signupSchema,
    },
    onSubmit: async ({ value }) => {
      userMutation.mutate(value);
    },
  });

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <CardTitle className="font-bold text-2xl">Create Account</CardTitle>
        <CardDescription>Join grillMyCV as a content creator</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <GoogleButton />
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <Separator className="w-full" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Or sign up with email
            </span>
          </div>
        </div>
        <form
          className="space-y-4"
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          <form.Field
            name="name"
            children={(field) => (
              <div>
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <div className="relative">
                    <User className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="name"
                      type="text"
                      placeholder="Enter your full name"
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                <FieldError field={field} />
              </div>
            )}
          />

          <form.Field
            name="email"
            children={(field) => (
              <div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                <FieldError field={field} />
              </div>
            )}
          />

          <form.Field
            name="password"
            children={(field) => (
              <div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <PasswordInput
                    id="password"
                    placeholder="Create a password"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    className="pr-10"
                    required
                  />
                </div>
                <FieldError field={field} />
              </div>
            )}
          />

          <form.Field
            name="confirmPassword"
            validators={{
              onChange: ({ value, fieldApi }) => {
                return value === fieldApi.form.getFieldValue("password")
                  ? undefined
                  : "Passwords do not match";
              },
            }}
            children={(field) => (
              <div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <PasswordInput
                    id="confirmPassword"
                    placeholder="Confirm your password"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    className="pr-10"
                    required
                  />
                </div>
                <FieldError field={field} />
              </div>
            )}
          />

          {/* <div className="flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={formData.agreeToTerms}
                onCheckedChange={(checked) => handleInputChange("agreeToTerms", checked as boolean)}
              />
              <Label htmlFor="terms" className="text-sm text-muted-foreground">
                I agree to the{" "}
                <Link to="/terms" className="text-primary hover:underline">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link to="/privacy" className="text-primary hover:underline">
                  Privacy Policy
                </Link>
              </Label>
            </div> */}

          <form.Subscribe
            selector={(state) => [state.canSubmit, state.isSubmitting]}
            children={([canSubmit, isSubmitting]) => (
              <Button type="submit" className="w-full" disabled={!canSubmit}>
                {isSubmitting ? "..." : "Create Account"}
              </Button>
            )}
          />
        </form>
      </CardContent>
      <CardFooter className="flex-col gap-5">
        <div className="text-center text-muted-foreground text-sm">
          Don't have an account?{" "}
          <Link href={"/auth/signin"} className="text-primary hover:underline">
            Sign in
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
};

export default SignupPage;
