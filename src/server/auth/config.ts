import { PrismaAdapter } from "@auth/prisma-adapter";
import {
	CredentialsSignin,
	type DefaultSession,
	type NextAuthConfig,
} from "next-auth";
import { encode as defaultEncode } from "next-auth/jwt";
import Credentials from "next-auth/providers/credentials";
import Google from "next-auth/providers/google";
import { v4 as uuid } from "uuid";
import type z from "zod";
import { ZodError } from "zod";

import { db } from "@/server/db";
import { comparePassword, hashPassword } from "@/utils/bcrypt";
import { signinSchema } from "@/validators/auth";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
	interface Session extends DefaultSession {
		user: {
			id: string;
			// ...other properties
			// role: UserRole;
		} & DefaultSession["user"];
	}

	// interface User {
	//   // ...other properties
	//   // role: UserRole;
	// }
}

class CustomAuthError extends CredentialsSignin {
	constructor(message: string) {
		super(message);
		this.code = message;
	}
}

const adapter = PrismaAdapter(db);

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
	providers: [
		Google({
			allowDangerousEmailAccountLinking: true,
		}),
		Credentials({
			credentials: {
				email: { label: "Email", type: "email" },
				password: { label: "Password", type: "password" },
			},
			authorize: async (credentials) => {
				try {
					await signinSchema.parseAsync(credentials);

					const { email, password } = credentials as z.infer<
						typeof signinSchema
					>;

					const existingUser = await db.user.findUnique({ where: { email } });
					if (!existingUser) {
						throw new CustomAuthError("Invalid credentials");
					}

					//if user does not have password than means they signed up with google
					if (!existingUser.password) {
						throw new CustomAuthError("Please signin with google");
					}

					const isPasswordValid = await comparePassword(
						password,
						existingUser.password,
					);
					if (!isPasswordValid) {
						throw new CustomAuthError("Invalid credentials");
					}
					return existingUser;
				} catch (error) {
					if (error instanceof ZodError) {
						return null;
					}
					throw error;
				}
			},
		}),
		/**
		 * ...add more providers here.
		 *
		 * Most other providers require a bit more work than the Discord provider. For example, the
		 * GitHub provider requires you to add the `refresh_token_expires_in` field to the Account
		 * model. Refer to the NextAuth.js docs for the provider you want to use. Example:
		 *
		 * @see https://next-auth.js.org/providers/github
		 */
	],
	adapter,
	callbacks: {
		session: ({ session, user }) => {
			return {
				...session,
				user: {
					...session.user,
					id: user.id,
				},
			};
		},
		jwt: ({ token, user, account }) => {
			if (account?.provider === "credentials") {
				token.credentials = true;
			}
			return token;
		},
	},
	jwt: {
		encode: async (params) => {
			if (params.token?.credentials) {
				const sessionToken = uuid();

				if (!params.token.sub) {
					throw new Error("No user ID found in token");
				}

				const createdSession = await adapter?.createSession?.({
					sessionToken: sessionToken,
					userId: params.token.sub,
					expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
				});

				if (!createdSession) {
					throw new Error("Failed to create session");
				}

				return sessionToken;
			}
			return defaultEncode(params);
		},
	},
	pages: {
		signIn: "/auth/signin",
	},
} satisfies NextAuthConfig;
