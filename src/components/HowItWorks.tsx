import { Search, Upload, MessageSquare } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export function HowItWorks() {
  const steps = [
    {
      icon: Search,
      title: "Find a Creator's Form",
      description:
        "Browse active collection forms from your favorite content creators across platforms.",
      step: "01",
    },
    {
      icon: Upload,
      title: "Submit Your CV",
      description:
        "Fill out the form and upload your resume to be featured in their upcoming streams.",
      step: "02",
    },
    {
      icon: MessageSquare,
      title: "Get Roasted & Feedback",
      description:
        "Watch the creator's stream and receive personalized feedback on your submitted CV.",
      step: "03",
    },
  ];

  return (
    <section id="how-it-works" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">How It Works</h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Simple process to submit your CV and get featured in creator streams
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {steps.map((step, index) => (
            <Card
              key={index}
              className="relative border-0 shadow-soft hover:shadow-lg transition-all duration-300"
            >
              <CardHeader className="text-center pb-4">
                <div className="relative mb-4">
                  <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto">
                    <step.icon className="h-8 w-8 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary text-white text-sm font-bold rounded-full flex items-center justify-center">
                    {step.step}
                  </div>
                </div>
                <CardTitle className="text-xl mb-2">{step.title}</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-base leading-relaxed">
                  {step.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
