import React from "react";

const CreatFormLayout = ({
  children,
}: Readonly<{ children: React.ReactNode }>) => {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">Create Collection Form</h1>
            <p className="text-muted-foreground text-lg">
              Set up a new CV collection form for your roasting session
            </p>
          </div>
          {children}
        </div>
      </div>
    </div>
  );
};

export default CreatFormLayout;
