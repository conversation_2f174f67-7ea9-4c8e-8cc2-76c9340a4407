"use client";

import { Search } from "lucide-react";
import React from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { debounce } from "@tanstack/react-pacer";

import { Input } from "@/components/ui/input";

const SearchInput = ({ defaultValue }: { defaultValue: string }) => {
  const params = useSearchParams();
  const { replace } = useRouter();
  const pathName = usePathname();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const searchParams = new URLSearchParams(params);
    if (value) {
      searchParams.set("q", value);
    } else {
      searchParams.delete("q");
    }
    searchParams.set("page", "1");
    searchParams.toString();

    replace(`${pathName}?${searchParams.toString()}`);
  };

  const debouncedHandleChange = debounce(handleChange, {
    wait: 500,
  });
  return (
    <div className="relative flex-1">
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        placeholder="Search forms, creators, or categories..."
        defaultValue={defaultValue}
        onChange={debouncedHandleChange}
        className="pl-10"
      />
    </div>
  );
};

export default SearchInput;
