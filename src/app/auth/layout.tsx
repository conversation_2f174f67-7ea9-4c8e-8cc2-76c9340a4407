import type React from "react";

import { auth } from "@/server/auth";
import { redirect } from "next/navigation";

const AuthLayout = async ({
	children,
}: Readonly<{ children: React.ReactNode }>) => {
	const session = await auth();
	if (session) redirect("/dashboard");
	return (
		<div className="flex min-h-screen items-center justify-center bg-background px-4">
			{children}
		</div>
	);
};

export default AuthLayout;
