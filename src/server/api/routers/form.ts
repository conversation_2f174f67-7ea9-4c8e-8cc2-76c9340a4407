import { z, ZodError } from "zod";

import {
    createTRPCRouter,
    protectedProcedure,
    publicProcedure,
} from "@/server/api/trpc";
import { createFormSchema, customCreateFormFieldSchema } from "@/validators/form";
import { TRPCError } from "@trpc/server";

export const formRouter = createTRPCRouter({
    create: protectedProcedure
        .input(createFormSchema)
        .mutation(async ({ ctx, input }) => {
            try {
                await createFormSchema.parseAsync(input);
                const { formTitle, category, description, streamingPlatform, streamURL, maxSubmissions, endDate, customFields } = input;

                const form = await ctx.db.form.create({
                    data: {
                        formTitle,
                        category,
                        description,
                        streamingPlatform,
                        streamURL,
                        maxSubmissions,
                        endDate,
                        createdBy: { connect: { id: ctx.session.user.id } },
                        customFields: {
                            create: customFields.map((field) => {
                                return {
                                    label: field.label,
                                    type: field.type,
                                    placeholder: field.placeholder,
                                    options: field.options,
                                    required: field.required,
                                };
                            })
                        },
                    }
                });
                return form;
            } catch (error) {
                if (error instanceof TRPCError) {
                    throw error;
                }
                if (error instanceof ZodError) {
                    throw new TRPCError({ code: "BAD_REQUEST", message: "Invalid input" });
                }
                throw new TRPCError({
                    code: "INTERNAL_SERVER_ERROR",
                    message: "Something went wrong",
                });
            }
        }),
    getTop3: publicProcedure
        .query(async ({ ctx }) => {
            const forms = await ctx.db.form.findMany({
                take: 3,
                orderBy: { createdAt: "desc" },
                include: {
                    createdBy: {
                        select: {
                            name: true,
                        },
                    }
                }
            });

            return forms;
        }),
});
