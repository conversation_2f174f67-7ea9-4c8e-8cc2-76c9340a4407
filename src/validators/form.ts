import { z } from "zod";

export const customCreateFormFieldSchema = z.object({
    id: z.string().min(1, "ID is required"),
    label: z.string().min(1, "Label is required"),
    type: z.enum(["text", "select", "number", "textarea"]),
    placeholder: z.string().optional(),
    options: z.string().trim().optional(),
    required: z.boolean(),
});

export const createFormSchema = z.object({
    formTitle: z.string().min(1, "Form title is required"),
    category: z.string().min(1, "Category is required"),
    description: z.string().min(1, "Description is required"),
    streamingPlatform: z.string().optional(),
    streamURL: z.union([z.literal(""), z.string().trim().url()]),
    maxSubmissions: z.number().min(1, "Max submissions must be greater than 0"),
    endDate: z.date().min(new Date(), "End date must be in the future"),
    customFields: z.array(customCreateFormFieldSchema),
});
