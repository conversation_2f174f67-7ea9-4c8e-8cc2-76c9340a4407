import { Flame } from "lucide-react";

export function Footer() {
  return (
    <footer className="border-t bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Flame className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">grillMyCV</span>
            </div>
            <p className="text-sm text-muted-foreground max-w-xs">
              Get your resume professionally roasted by content creators and improve your job prospects.
            </p>
          </div>

          {/* For Users */}
          <div className="space-y-4">
            <h4 className="font-semibold">For Users</h4>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div><a href="#" className="hover:text-foreground transition-colors">Submit CV</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Live Sessions</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Feedback History</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Pricing</a></div>
            </div>
          </div>

          {/* For Creators */}
          <div className="space-y-4">
            <h4 className="font-semibold">For Creators</h4>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div><a href="#" className="hover:text-foreground transition-colors">Start Streaming</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Creator Dashboard</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Monetization</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Guidelines</a></div>
            </div>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h4 className="font-semibold">Support</h4>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div><a href="#" className="hover:text-foreground transition-colors">Help Center</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Privacy Policy</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Terms of Service</a></div>
              <div><a href="#" className="hover:text-foreground transition-colors">Contact</a></div>
            </div>
          </div>
        </div>

        <div className="border-t mt-12 pt-8 text-center text-sm text-muted-foreground">
          <p>&copy; 2024 grillMyCV. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}