import { Users, Calendar, FileText } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { api } from "@/trpc/server";

export async function CollectionForms() {
  const top3Forms = await api.form.getTop3();
  return (
    <section id="forms" className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Active Collection Forms
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Submit your CV to content creators and get featured in their
            roasting streams
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {top3Forms.map((form) => {
            const isFormActive = form.endDate > new Date();

            return (
              <Card
                key={form.id}
                className="relative overflow-hidden hover:shadow-lg transition-all duration-300"
              >
                {isFormActive && (
                  <div className="absolute top-3 right-3 z-10">
                    <Badge
                      variant="default"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      OPEN
                    </Badge>
                  </div>
                )}
                {!isFormActive && (
                  <div className="absolute top-3 right-3 z-10">
                    <Badge variant="secondary">CLOSED</Badge>
                  </div>
                )}

                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-1">
                        {form.formTitle}
                      </CardTitle>
                      <CardDescription className="font-medium">
                        by {form.createdBy.name}
                      </CardDescription>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    {form.description}
                  </p>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    {/* <div className="flex items-center space-x-1">
                    <FileText className="h-4 w-4" />
                    <span>{form.submissions} submitted</span>
                  </div> */}
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{form.endDate.toDateString()}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    {form.streamingPlatform && (
                      <span>
                        Streaming on:{" "}
                        <span className="font-medium text-foreground">
                          {form.streamingPlatform}
                        </span>
                      </span>
                    )}
                    {form.streamURL && (
                      <Button
                        variant="link"
                        size="sm"
                        className="h-auto p-0 text-xs text-primary hover:text-primary/80"
                        asChild
                      >
                        <a
                          href={`${form.streamURL}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Watch Live
                        </a>
                      </Button>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge variant="secondary">{form.category}</Badge>
                    <Button
                      variant={isFormActive ? "fire" : "outline"}
                      size="sm"
                      className="w-32"
                      disabled={!isFormActive}
                      asChild={isFormActive}
                    >
                      {isFormActive ? (
                        <a href="/submit-cv">Get Grilled 🔥</a>
                      ) : (
                        "Closed"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="text-center">
          <Button variant="outline" size="lg" asChild>
            <a href="/all-forms">
              <Users className="mr-2 h-5 w-5" />
              View All Forms
            </a>
          </Button>
        </div>
      </div>
    </section>
  );
}
