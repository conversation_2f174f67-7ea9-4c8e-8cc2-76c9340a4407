{"name": "grill-my-cv", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "postinstall": "prisma generate", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@prisma/client": "^6.5.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-form": "^1.23.1", "@tanstack/react-pacer": "^0.16.4", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.11.0", "react-dom": "^19.0.0", "server-only": "^0.0.1", "sonner": "^2.0.7", "superjson": "^2.2.1", "tailwind-merge": "^3.3.1", "uuid": "^13.0.0", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.0.15", "@types/bcrypt": "^6.0.0", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "postcss": "^8.5.3", "prisma": "^6.5.0", "tailwindcss": "^4.0.15", "tw-animate-css": "^1.4.0", "typescript": "^5.8.2"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.6.3"}