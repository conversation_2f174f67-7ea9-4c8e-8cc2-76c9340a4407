import type { AnyFieldApi } from "@tanstack/react-form";
import React from "react";

const FieldError = ({ field }: { field: AnyFieldApi }) => {
	return (
		<div>
			{field.state.meta.isTouched && !field.state.meta.isValid ? (
				<em className="text-destructive text-sm">
					{field.state.meta.errors
						.map((err: any) => (typeof err === "string" ? err : err.message))
						.join(", ")}
				</em>
			) : null}
		</div>
	);
};

export default FieldError;
