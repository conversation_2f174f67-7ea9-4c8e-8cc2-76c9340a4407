"use client";

import React, { useState } from "react";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { format } from "date-fns";
import { CalendarIcon, Plus, Save, Trash2 } from "lucide-react";
import { v4 as uuid } from "uuid";
import { toast } from "sonner";

import {
  createFormSchema,
  customCreateFormFieldSchema,
} from "@/validators/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import FieldError from "@/components/FieldError";
import { api } from "@/trpc/react";
import { useRouter } from "next/navigation";
import { FORM_CATEGORIES } from "@/common/constants";

type CreateFormType = z.infer<typeof createFormSchema>;
const formDefaultValues: CreateFormType = {
  formTitle: "",
  category: "",
  description: "",
  streamingPlatform: undefined,
  streamURL: "",
  maxSubmissions: 1,
  endDate: new Date(new Date().setDate(new Date().getDate() - 1)),
  customFields: [],
};

type CustomFieldType = z.infer<typeof customCreateFormFieldSchema>;
const customFieldDefaultValues: CustomFieldType = {
  id: uuid(),
  label: "",
  type: "text",
  placeholder: undefined,
  options: undefined,
  required: false,
};

const CreateFormPage = () => {
  const form = useForm({
    defaultValues: formDefaultValues,
    validators: {
      onChange: createFormSchema,
    },
    onSubmit: async ({ value }) => {
      createFormMutation.mutate(value);
    },
  });
  const router = useRouter();

  const [customFieldTypes, setCustomFieldTypes] =
    useState<Partial<CustomFieldType>[]>();
  const createFormMutation = api.form.create.useMutation({
    onSuccess: () => {
      form.reset();
      toast.success("Form created successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
    onSettled: () => {
      router.replace("/dashboard");
    },
  });

  const addCustomField = () => {
    const newField = { ...customFieldDefaultValues, id: uuid() };
    form.setFieldValue("customFields", [
      ...form.getFieldValue("customFields"),
      newField,
    ]);

    setCustomFieldTypes((prev) => {
      if (!prev) {
        return [newField];
      }
      return [...prev, newField];
    });
  };

  const removeCustomField = (id: string) => {
    form.setFieldValue(
      "customFields",
      form.getFieldValue("customFields").filter((field) => field.id !== id)
    );
  };

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Set up the basic details of your collection form
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <form.Field
                  name="formTitle"
                  children={(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="title">Form Title *</Label>
                      <Input
                        id="title"
                        placeholder="e.g., Tech Resume Review Collection"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        required
                        onBlur={field.handleBlur}
                      />
                      <FieldError field={field} />
                    </div>
                  )}
                />
                <form.Field
                  name="category"
                  children={(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="category">Category *</Label>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        defaultValue="Select category"
                        value={field.state.value}
                        required
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {FORM_CATEGORIES.map((category) => (
                            <SelectItem key={category.value} value={category.value}>
                              {category.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FieldError field={field} />
                    </div>
                  )}
                />
              </div>

              <form.Field
                name="description"
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      placeholder="Describe what kind of CVs you're looking for and what participants can expect..."
                      rows={3}
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      required
                      onBlur={field.handleBlur}
                    />
                    <FieldError field={field} />
                  </div>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <form.Field
                  name="streamingPlatform"
                  children={(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="platform">Streaming Platform</Label>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        value={field.state.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Where will you stream?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="youtube">YouTube</SelectItem>
                          <SelectItem value="twitch">Twitch</SelectItem>
                          <SelectItem value="linkedin">
                            LinkedIn Live
                          </SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FieldError field={field} />
                    </div>
                  )}
                />
                <form.Field
                  name="streamURL"
                  children={(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="stream-url">Stream URL</Label>
                      <Input
                        id="stream-url"
                        placeholder="https://youtube.com/..."
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                      <FieldError field={field} />
                    </div>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Collection Settings</CardTitle>
              <CardDescription>
                Configure submission limits and deadlines
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <form.Field
                  name="maxSubmissions"
                  children={(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="max-submissions">Max Submissions *</Label>
                      <Input
                        id="max-submissions"
                        type="number"
                        placeholder="50"
                        min="1"
                        value={field.state.value}
                        onChange={(e) =>
                          field.handleChange(Number(e.target.value))
                        }
                        onBlur={field.handleBlur}
                      />
                      <FieldError field={field} />
                    </div>
                  )}
                />
                <form.Field
                  name="endDate"
                  children={(field) => (
                    <div className="space-y-2">
                      <Label>Submission Deadline *</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !field.state.meta.isTouched &&
                                "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.state.meta.isTouched ? (
                              format(field.state.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.state.value}
                            onSelect={(value) => field.handleChange(value)}
                            autoFocus
                            className={cn("p-3 pointer-events-auto")}
                            required
                          />
                        </PopoverContent>
                      </Popover>
                      <FieldError field={field} />
                    </div>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Additional Fields</CardTitle>
              <CardDescription>
                Add custom fields to collect more information from applicants
              </CardDescription>
            </CardHeader>
            <form.Field
              name="customFields"
              mode="array"
              children={(field) => (
                <CardContent>
                  {field.state.value.map((item, index) => {
                    return (
                      <div
                        key={item.id}
                        className="border rounded-lg p-4 space-y-3"
                      >
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Field {index + 1}</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeCustomField(item.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <form.Field
                            key={uuid()}
                            name={`customFields[${index}].label`}
                            children={(subfield) => (
                              <div className="space-y-2">
                                <Label>Field Label *</Label>
                                <Input
                                  placeholder="e.g., Years of Experience"
                                  value={subfield.state.value}
                                  onChange={(e) =>
                                    subfield.handleChange(e.target.value)
                                  }
                                />
                                <FieldError field={subfield} />
                              </div>
                            )}
                          />

                          <form.Field
                            key={uuid()}
                            name={`customFields[${index}].type`}
                            children={(subfield) => (
                              <div className="space-y-2">
                                <Label>Field Type</Label>
                                <Select
                                  value={subfield.state.value}
                                  onValueChange={(value) => {
                                    setCustomFieldTypes((prev) => {
                                      if (!prev) {
                                        return [];
                                      }
                                      return prev.map((field) => {
                                        if (field.id === item.id) {
                                          return {
                                            ...field,
                                            type: value as any as CustomFieldType["type"],
                                          };
                                        }
                                        return field;
                                      });
                                    });
                                    subfield.handleChange(
                                      value as any as CustomFieldType["type"]
                                    );
                                  }}
                                >
                                  <SelectTrigger className="w-full">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="text">Text</SelectItem>
                                    <SelectItem value="select">
                                      Dropdown
                                    </SelectItem>
                                    <SelectItem value="textarea">
                                      Long Text
                                    </SelectItem>
                                    <SelectItem value="number">
                                      Number
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            )}
                          />
                        </div>
                        <form.Field
                          key={uuid()}
                          name={`customFields[${index}].placeholder`}
                          children={(subfield) => (
                            <div className="space-y-2">
                              <Label>Placeholder</Label>
                              <Input
                                placeholder="e.g., Enter your name"
                                value={subfield.state.value}
                                onChange={(e) =>
                                  subfield.handleChange(e.target.value)
                                }
                              />
                              <FieldError field={subfield} />
                            </div>
                          )}
                        />
                        {customFieldTypes &&
                          customFieldTypes[index]?.type === "select" && (
                            <form.Field
                              key={uuid()}
                              name={`customFields[${index}].options`}
                              children={(subfield) => (
                                <div className="space-y-2">
                                  <Label>Options (comma separated)</Label>
                                  <Textarea
                                    placeholder={`Entry Level\nMid Level\nSenior Level`}
                                    rows={2}
                                    value={subfield.state.value}
                                    onChange={(e) =>
                                      subfield.handleChange(e.target.value)
                                    }
                                  />
                                  <FieldError field={subfield} />
                                </div>
                              )}
                            />
                          )}

                        <form.Field
                          key={uuid()}
                          name={`customFields[${index}].required`}
                          children={(subfield) => (
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={subfield.state.value}
                                onCheckedChange={(checked) =>
                                  subfield.handleChange(checked)
                                }
                              />
                              <Label>Required field *</Label>
                            </div>
                          )}
                        />
                      </div>
                    );
                  })}
                  <Button
                    variant="outline"
                    onClick={addCustomField}
                    className="w-full cursor-pointer"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Custom Field
                  </Button>
                </CardContent>
              )}
            />
          </Card>
        </div>
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* Actions */}
            <div className="space-y-3">
              <Button
                variant="fire"
                size="lg"
                className="w-full cursor-pointer"
                type="submit"
              >
                <Save className="mr-2 h-5 w-5" />
                Create Form
              </Button>
            </div>

            {/* Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">💡 Tips</CardTitle>
              </CardHeader>
              <CardContent className="text-sm space-y-2">
                <p>• Clear titles get more submissions</p>
                <p>• Set realistic deadlines</p>
                <p>• Add custom fields for better targeting</p>
                <p>• Preview your form before publishing</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </form>
  );
};

export default CreateFormPage;
