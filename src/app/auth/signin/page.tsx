"use client";

import { useForm } from "@tanstack/react-form";
import { Mail } from "lucide-react";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import React from "react";
import type { z } from "zod";

import FieldError from "@/components/FieldError";
import GoogleButton from "@/components/auth/GoogleButton";
import PasswordInput from "@/components/auth/PasswordInput";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { signinSchema } from "@/validators/auth";

type SigninFormType = z.infer<typeof signinSchema>;
const formDefaultValues: SigninFormType = {
	email: "",
	password: "",
};

const SigninPage = () => {
	const searchParams = useSearchParams();
	const errorMessage = searchParams.get("code");
	const errorType = searchParams.get("error");
	const form = useForm({
		defaultValues: formDefaultValues,
		validators: {
			onChange: signinSchema,
		},
		onSubmit: async ({ value }) => {
			console.log(value);
			signIn("credentials", {
				email: value.email,
				password: value.password,
				redirectTo: "/dashboard",
			});
		},
	});

	return (
		<Card className="w-full max-w-md">
			<CardHeader className="text-center">
				<CardTitle className="font-bold text-2xl">Welcome Back</CardTitle>
				<CardDescription>
					Sign in to your content creator account
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				<GoogleButton />
				<div className="relative">
					<div className="absolute inset-0 flex items-center">
						<Separator className="w-full" />
					</div>
					<div className="relative flex justify-center text-xs uppercase">
						<span className="bg-background px-2 text-muted-foreground">
							Or continue with email
						</span>
					</div>
				</div>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						e.stopPropagation();
						form.handleSubmit();
					}}
				>
					<div className="flex flex-col gap-6">
						<form.Field
							name="email"
							children={(field) => (
								<div>
									<div className="grid gap-2">
										<Label htmlFor={field.name}>Email</Label>
										<div className="relative">
											<Mail className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
											<Input
												id={field.name}
												type={field.name}
												placeholder="<EMAIL>"
												required
												className="pl-10"
												value={field.state.value}
												onBlur={field.handleBlur}
												onChange={(e) => field.handleChange(e.target.value)}
											/>
										</div>
									</div>
									<FieldError field={field} />
								</div>
							)}
						/>
						<form.Field
							name="password"
							children={(field) => (
								<div>
									<div className="grid gap-2">
										<div className="flex items-center">
											<Label htmlFor={field.name}>Password</Label>
										</div>
										<PasswordInput
											id={field.name}
											type={field.name}
											required
											onBlur={field.handleBlur}
											onChange={(e) => field.handleChange(e.target.value)}
											value={field.state.value}
										/>
									</div>
									<FieldError field={field} />
								</div>
							)}
						/>

						<form.Subscribe
							selector={(state) => [
								state.canSubmit,
								state.isSubmitting,
								state.isSubmitted,
							]}
							children={([canSubmit, isSubmitting]) => (
								<Button
									type="submit"
									className="w-full cursor-pointer"
									disabled={!canSubmit}
								>
									{isSubmitting ? "..." : "Login"}
								</Button>
							)}
						/>
					</div>

					{errorType === "CredentialsSignin" && (
						<p className="text-destructive text-sm">
							{errorMessage && decodeURIComponent(errorMessage)}
						</p>
					)}
				</form>
			</CardContent>
			<CardFooter className="mt-2 flex-col">
				<div className="text-center text-muted-foreground text-sm">
					Don't have an account?{" "}
					<Link href={"/auth/signup"} className="text-primary hover:underline">
						Sign up
					</Link>
				</div>
			</CardFooter>
		</Card>
	);
};

export default SigninPage;
