import "@/styles/globals.css";

import type { Metada<PERSON> } from "next";
import { SessionProvider } from "next-auth/react";
import { <PERSON>ei<PERSON> } from "next/font/google";

import { Toaster } from "@/components/ui/sonner";
import { TRPCReactProvider } from "@/trpc/react";

export const metadata: Metadata = {
	title: "Create T3 App",
	description: "Generated by create-t3-app",
	icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const geist = Geist({
	subsets: ["latin"],
	variable: "--font-geist-sans",
});

export default function RootLayout({
	children,
}: Readonly<{ children: React.ReactNode }>) {
	return (
		<html lang="en" className={`${geist.variable}`}>
			<body>
				<TRPCReactProvider>
					<SessionProvider>{children}</SessionProvider>
					<Toaster richColors position="top-center" />
				</TRPCReactProvider>
			</body>
		</html>
	);
}
